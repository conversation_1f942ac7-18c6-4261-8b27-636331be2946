export default class MainMenu extends Phaser.Scene
{
    constructor ()
    {
        super('MainMenu');

        this.music;
        this.musicButton;
        this.musicEnabled;
    }

    create (data)
    {
        this.musicEnabled = this.registry.get('musicEnabled');

        // 如果从游戏场景传回了音乐对象，使用它
        if (data && data.music) {
            this.music = data.music;
        }

        let background = this.add.image(400, 300, 'background');

        this.tweens.add({
            targets: background,
            alpha: { from: 0, to: 1 },
            duration: 1000
        });

        const fontStyle = {
            fontFamily: 'Arial',
            fontSize: 48,
            color: '#ffffff',
            fontStyle: 'bold',
            padding: 16,
            shadow: {
                color: '#000000',
                fill: true,
                offsetX: 2,
                offsetY: 2,
                blur: 4
            }
        };

        const buttonStyle = {
            fontFamily: 'Arial',
            fontSize: 32,
            color: '#ffffff',
            fontStyle: 'bold',
            padding: 8,
            shadow: {
                color: '#000000',
                fill: true,
                offsetX: 2,
                offsetY: 2,
                blur: 4
            }
        };

        this.add.text(20, 20, 'High Score: ' + this.registry.get('highscore'), fontStyle);

        // 添加音乐开关按钮
        this.musicButton = this.add.text(650, 20, this.musicEnabled ? '🔊 ON' : '🔇 OFF', buttonStyle);
        this.musicButton.setInteractive({ useHandCursor: true });
        this.musicButton.on('pointerdown', this.toggleMusic, this);
        this.musicButton.on('pointerover', () => this.musicButton.setTint(0xffff00));
        this.musicButton.on('pointerout', () => this.musicButton.clearTint());

        let logo = this.add.image(400, -200, 'logo');

        // 根据音乐开关状态播放音乐
        if (this.musicEnabled && !this.music)
        {
            this.music = this.sound.play('music', { loop: true });
        }

        this.tweens.add({
            targets: logo,
            y: 300,
            ease: 'bounce.out',
            duration: 1200
        });

        this.input.once('pointerdown', (pointer) => {
            // 确保点击的不是音乐按钮
            if (pointer.x < 650 || pointer.x > 750 || pointer.y < 20 || pointer.y > 60) {
                // 将音乐对象传递给游戏场景
                this.scene.start('MainGame', { music: this.music });
            }
        });
    }

    toggleMusic ()
    {
        this.musicEnabled = !this.musicEnabled;
        this.registry.set('musicEnabled', this.musicEnabled);

        this.musicButton.setText(this.musicEnabled ? '🔊 ON' : '🔇 OFF');

        if (this.musicEnabled)
        {
            // 开启音乐，从头播放
            if (this.music) {
                this.music.stop();
            }
            this.music = this.sound.play('music', { loop: true });
        }
        else
        {
            // 关闭音乐
            if (this.music) {
                this.music.stop();
                this.music = null;
            }
        }
    }
}