#!/usr/bin/env python3
import os
import urllib.request
import json
from pathlib import Path

def create_directories():
    """创建必要的目录结构"""
    directories = [
        'assets',
        'assets/sounds'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {directory}")

def download_file(url, local_path):
    """下载单个文件"""
    try:
        print(f"下载: {url} -> {local_path}")
        urllib.request.urlretrieve(url, local_path)
        print(f"✓ 下载完成: {local_path}")
        return True
    except Exception as e:
        print(f"✗ 下载失败: {local_path} - {e}")
        return False

def create_placeholder_files():
    """创建占位符文件"""
    
    # 创建简单的 emojis.json
    emojis_json = {
        "frames": {}
    }
    
    # 生成40个emoji帧的配置
    for i in range(1, 41):
        emojis_json["frames"][f"smile{i}"] = {
            "frame": {"x": 0, "y": 0, "w": 64, "h": 64},
            "rotated": False,
            "trimmed": False,
            "spriteSourceSize": {"x": 0, "y": 0, "w": 64, "h": 64},
            "sourceSize": {"w": 64, "h": 64}
        }
    
    with open('assets/emojis.json', 'w') as f:
        json.dump(emojis_json, f, indent=2)
    print("✓ 创建 emojis.json")

def main():
    print("开始下载 Emoji Match 游戏资源...")
    
    # 创建目录
    create_directories()
    
    # 定义资源URL和本地路径
    resources = [
        # 图片资源
        ('https://cdn.phaserfiles.com/v385/assets/games/emoji-match/background.png', 'assets/background.png'),
        ('https://cdn.phaserfiles.com/v385/assets/games/emoji-match/logo.png', 'assets/logo.png'),
        ('https://cdn.phaserfiles.com/v385/assets/games/emoji-match/emojis.png', 'assets/emojis.png'),
        ('https://cdn.phaserfiles.com/v385/assets/games/emoji-match/emojis.json', 'assets/emojis.json'),
        
        # 音频资源
        ('https://cdn.phaserfiles.com/v385/assets/games/emoji-match/sounds/music.mp3', 'assets/sounds/music.mp3'),
        ('https://cdn.phaserfiles.com/v385/assets/games/emoji-match/sounds/countdown.mp3', 'assets/sounds/countdown.mp3'),
        ('https://cdn.phaserfiles.com/v385/assets/games/emoji-match/sounds/match.mp3', 'assets/sounds/match.mp3'),
        ('https://cdn.phaserfiles.com/v385/assets/games/emoji-match/sounds/music.ogg', 'assets/sounds/music.ogg'),
        ('https://cdn.phaserfiles.com/v385/assets/games/emoji-match/sounds/countdown.ogg', 'assets/sounds/countdown.ogg'),
        ('https://cdn.phaserfiles.com/v385/assets/games/emoji-match/sounds/match.ogg', 'assets/sounds/match.ogg'),
    ]
    
    # 下载资源
    success_count = 0
    total_count = len(resources)
    
    for url, local_path in resources:
        if download_file(url, local_path):
            success_count += 1
    
    # 如果某些文件下载失败，创建占位符
    if not os.path.exists('assets/emojis.json'):
        create_placeholder_files()
    
    print(f"\n下载完成: {success_count}/{total_count} 个文件成功")
    
    if success_count < total_count:
        print("部分文件下载失败，但游戏仍可运行")
    
    print("\n资源已准备就绪！现在可以启动游戏了。")

if __name__ == "__main__":
    main()